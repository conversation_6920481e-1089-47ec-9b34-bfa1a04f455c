<!DOCTYPE html>
<html lang="en-GB">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>SailorPay Improved - Navigating Payments, Your Way</title>

    <!-- favicons-->
    <link rel="icon" type="image/svg+xml" href="images/logos/favicon.svg">
    <link rel="apple-touch-icon" sizes="180x180" href="apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="favicon-16x16.png">
    <link rel="manifest" href="site.webmanifest">
    <link rel="mask-icon" href="safari-pinned-tab.svg" color="#226191">
    <meta name="msapplication-TileColor" content="#226191">
    <meta name="theme-color" content="#ffffff">

    <!-- Google Fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Archivo+Black&display=swap">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

    <!-- Main CSS -->
    <link rel="stylesheet" href="css/variables.css" type="text/css" media="all">
    <link rel="stylesheet" href="css/style.css" type="text/css" media="all">
    <link rel="stylesheet" href="css/fixes.css" type="text/css" media="all">
    <link rel="stylesheet" href="css/footer.css" type="text/css" media="all">
    <link rel="stylesheet" href="css/navbar.css" type="text/css" media="all">
    <link rel="stylesheet" href="css/common.css" type="text/css" media="all">
    <link rel="stylesheet" href="css/modern.css" type="text/css" media="all">
    <link rel="stylesheet" href="css/animations.css" type="text/css" media="all">
    <link rel="stylesheet" href="css/icons.css" type="text/css" media="all">
    <link rel="stylesheet" href="css/payment-logos.css" type="text/css" media="all">
    <link rel="stylesheet" href="css/hero-banner.css" type="text/css" media="all">

    <!-- Custom Contact Button Styles -->
    <style>
        /* Fix hero section overlapping issues */
        #hero.hero-section.full-width {
            position: relative !important;
            z-index: 1 !important;
            overflow: visible !important;
        }

        .hero-content {
            position: relative !important;
            z-index: 10 !important;
        }

        .contact-cta {
            clear: both;
            margin-top: 25px !important;
            position: relative !important;
            z-index: 1000 !important;
            overflow: visible !important;
        }

        .contact-btn {
            position: relative !important;
            z-index: 1001 !important;
            pointer-events: auto !important;
            display: inline-block !important;
            cursor: pointer !important;
            transition: all 0.3s ease !important;
            transform: translateY(0) !important;
            box-shadow: 0 4px 8px rgba(34, 97, 145, 0.3) !important;
        }

        .contact-btn:hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 6px 12px rgba(34, 97, 145, 0.4) !important;
            background-color: #184a73 !important;
            color: #ffffff !important;
        }

        .contact-btn:active {
            transform: translateY(0) !important;
            box-shadow: 0 2px 4px rgba(34, 97, 145, 0.3) !important;
        }

        .contact-btn i {
            margin-left: 8px;
            transition: transform 0.3s ease;
        }

        .contact-btn:hover i {
            transform: translateX(3px);
        }

        /* Ensure no other elements interfere */
        .full-width-banner::before {
            z-index: 1 !important;
        }

        .col.col--alignLeft.col-7.slide-in-left {
            position: relative !important;
            z-index: 10 !important;
        }
    </style>
</head>

<body class="page-template-default page">
    <div id="page" class="site siteWrapper">
        <a class="skip-link screen-reader-text" href="#content">Skip to content</a>

        <a name="top"></a>

        <header id="masthead" class="site-header">
            <div class="wrapper header-wrapper">
                <div class="site-branding">
                    <a href="index.html">
                        <img src="images/logo.png" alt="SailorPay" width="150" height="50">
                    </a>
                </div>

                <div class="header-contact-info">
                    <button type="button" aria-label="Menu" aria-controls="navigation" class="hamburger hamburger--spin" id="menu-toggle">
                        <span class="hamburger-box">
                            <span class="hamburger-inner"></span>
                        </span>
                    </button>
                </div>

                <nav id="site-navigation" class="main-navigation">
                    <ul id="primary-menu" class="menu">
                        <li class="menu-item current-menu-item"><a href="index.html">Home</a></li>
                        <li class="menu-item has-dropdown">
                            <a href="services.html">Services <i class="fas fa-chevron-down"></i></a>
                            <ul class="sub-menu">
                                <li class="menu-item"><a href="services.html#merchant-account">Merchant Account Setup</a></li>
                                <li class="menu-item"><a href="services.html#payment-gateway">Payment Gateway</a></li>
                                <li class="menu-item"><a href="services.html#payment-orchestration">Payment Orchestration</a></li>
                                <li class="menu-item"><a href="services.html#banking-services">Banking Services</a></li>
                                <li class="menu-item"><a href="services.html#payment-methods">Payment Methods</a></li>
                            </ul>
                        </li>
                        <li class="menu-item"><a href="#company">Company</a></li>
                        <li class="menu-item"><a href="contact.html">Contact Us</a></li>
                    </ul>
                </nav>
            </div>

            <div class="mobile-menu" id="mobile-menu" style="left: -210px;">
                <ul class="menu">
                    <li class="menu-item current-menu-item"><a href="index.html">Home</a></li>
                    <li class="menu-item has-dropdown">
                        <a href="services.html">Services <i class="fas fa-chevron-down"></i></a>
                        <ul class="sub-menu">
                            <li class="menu-item"><a href="services.html#merchant-account">Merchant Account Setup</a></li>
                            <li class="menu-item"><a href="services.html#payment-gateway">Payment Gateway</a></li>
                            <li class="menu-item"><a href="services.html#payment-orchestration">Payment Orchestration</a></li>
                            <li class="menu-item"><a href="services.html#banking-services">Banking Services</a></li>
                            <li class="menu-item"><a href="services.html#payment-methods">Payment Methods</a></li>
                        </ul>
                    </li>
                    <li class="menu-item"><a href="#company">Company</a></li>
                    <li class="menu-item"><a href="contact.html">Contact Us</a></li>
                </ul>
            </div>

        </header><!-- #masthead -->

        <div id="content" class="site-content">
            <div id="primary" class="content-area">
                <main id="main" class="site-main">
                    <section id="hero" class="hero-section full-width">
                        <div class="full-width-banner">
                            <div class="banner-content">
                                <h1 class="banner-title fade-in">Navigating Payments, Your Way</h1>
                            </div>
                        </div>
                        <div class="wrapper">
                            <div class="row">
                                <div class="col col--alignLeft col-7 slide-in-left">
                                    <div class="hero-content">
                                        <h2 class="hero-subtitle">Ready to partner with us for streamlined payment solutions?</h2>
                                        <p>Choose us for Merchant Account Setup, Payment Orchestration, and Banking Solutions - your business's right choice.</p>
                                        <div class="contact-cta" style="margin-top: 20px; position: relative; z-index: 100;">
                                            <a href="contact.html" class="button primary-button contact-btn" style="position: relative; z-index: 101; pointer-events: auto; display: inline-block; cursor: pointer;">Contact Us <i class="fas fa-arrow-right"></i></a>
                                        </div>
                                    </div>
                                </div>
                                <div class="col col--alignLeft col-5 slide-in-right">
                                    <div class="feature-image-container">
                                        <img src="images/features/global-payments.jpg" alt="SailorPay Payment Solutions" class="hero-image">
                                    </div>
                                </div>
                            </div><!--row-->
                        </div><!--wrapper-->
                    </section>

                    <section id="mission">
                        <div class="wrapper">
                            <div class="row">
                                <div class="col col--alignCenter col-12 slide-up">
                                    <h2>Our Mission</h2>
                                    <p class="mission-statement">Seamless and secure payment solutions for your business.</p>
                                </div>
                            </div><!--row-->

                            <div class="row">
                                <div class="col col--alignCenter col-12 fade-in">
                                    <div class="mission-box">
                                        <p>Sailorpay® is a leading ISO that specializes in offering comprehensive payment solutions for businesses of all sizes. With a commitment to seamless transactions, security, and financial innovation, we provide a range of services to meet the diverse needs of merchants across various industries.</p>
                                    </div>
                                </div>
                            </div><!--row-->
                        </div><!--wrapper-->
                    </section>

                    <section id="payment-methods">
                        <div class="wrapper">
                            <div class="row">
                                <div class="col col--alignCenter col-12 slide-up">
                                    <h2>Accepted Payment Methods</h2>
                                    <p>We support a wide range of payment methods to help you reach more customers.</p>

                                    <div class="icon-only-block fade-in">
                                        <div class="icon-container">
                                            <span class="icon icon--altpay--AltPayNewNew_VISA"></span>
                                        </div>
                                        <div class="icon-container">
                                            <span class="icon icon--altpay--AltPayNewNew_MasterCard"></span>
                                        </div>
                                        <div class="icon-container">
                                            <span class="icon icon--altpay--AltPayNewNew_AmericanExpress"></span>
                                        </div>
                                        <div class="icon-container">
                                            <span class="icon icon--altpay--AltPayNewNew_vPay"></span>
                                        </div>
                                        <div class="icon-container">
                                            <span class="icon icon--altpay--AltPayNewNew_ChinaUnionPay"></span>
                                        </div>
                                        <div class="icon-container">
                                            <span class="icon icon--altpay--AltPayNewNew_DinersClub"></span>
                                        </div>
                                        <div class="icon-container">
                                            <span class="icon icon--altpay--AltPayNewNew_JCB"></span>
                                        </div>
                                        <div class="icon-container">
                                            <span class="icon icon--altpay--AltPayNewNew_Discover"></span>
                                        </div>
                                        <div class="icon-container">
                                            <span class="icon icon--altpay--AltPayNewNew_Maestro"></span>
                                        </div>
                                    </div>
                                </div>
                            </div><!--row-->
                        </div><!--wrapper-->
                    </section>

                    <section id="company">
                        <div class="wrapper">
                            <div class="row">
                                <div class="col col--alignCenter col-12 slide-up">
                                    <h2>About SailorPay</h2>
                                    <p class="mission-statement">Your trusted partner in payment processing solutions.</p>
                                </div>
                            </div><!--row-->

                            <div class="row">
                                <div class="col col--alignLeft col-6 fade-in">
                                    <h3>Our Company</h3>
                                    <p>SailorPay is a leading Independent Sales Organization (ISO) specializing in comprehensive payment solutions for businesses of all sizes. Founded with a vision to simplify payment processing, we have grown to become a trusted partner for merchants across various industries.</p>
                                    <p>Our team of payment experts brings years of experience in the financial technology sector, ensuring that our clients receive the best possible service and support.</p>
                                </div>
                                <div class="col col--alignLeft col-6 fade-in">
                                    <h3>Our Values</h3>
                                    <ul>
                                        <li><strong>Security:</strong> We prioritize the security of every transaction with industry-leading encryption and compliance standards.</li>
                                        <li><strong>Innovation:</strong> We continuously evolve our technology to meet the changing needs of modern businesses.</li>
                                        <li><strong>Reliability:</strong> Our robust infrastructure ensures consistent, dependable payment processing.</li>
                                        <li><strong>Support:</strong> We provide exceptional customer service and technical support to help your business succeed.</li>
                                    </ul>
                                </div>
                            </div><!--row-->
                        </div><!--wrapper-->
                    </section>

                    <section id="cta">
                        <div class="wrapper">
                            <div class="row">
                                <div class="col col--alignCenter col-12 fade-in">
                                    <h2>Ready to Navigate the World of Payments?</h2>
                                    <p>Contact us today to learn more about our payment solutions and how we can help your business grow in an increasingly digital marketplace.</p>
                                    <p>SailorPay is your trusted partner for all payment processing needs, with a focus on security, reliability, and customer satisfaction.</p>
                                    <a href="contact.html" class="button">Contact Us <i class="fas fa-arrow-right"></i></a>
                                </div>
                            </div><!--row-->
                        </div><!--wrapper-->
                    </section>
                </main><!-- #main -->
            </div><!-- #primary -->
        </div><!-- #content -->

        <footer id="colophon" class="site-footer">
            <section class="footer-menus">
                <div class="wrapper">
                    <div class="row fade-in">
                        <div class="col col-2">
                            <span class="widget-title">Company</span>
                            <ul class="menu">
                                <li class="menu-item"><a href="index.html">Home</a></li>
                                <li class="menu-item"><a href="services.html">Services</a></li>
                                <li class="menu-item"><a href="contact.html">Contact Us</a></li>
                            </ul>

                            <span class="widget-title">Customer Care</span>
                            <ul class="menu">
                                <li class="menu-item"><a href="contact.html">Support</a></li>
                                <li class="menu-item"><a href="https://wa.me/************" target="_blank">WhatsApp</a></li>
                            </ul>
                        </div>

                        <div class="col col-2">
                            <span class="widget-title">Products</span>
                            <ul class="menu">
                                <li class="menu-item"><a href="services.html#merchant-account">Merchant Account Setup</a></li>
                                <li class="menu-item"><a href="services.html#payment-gateway">Payment Gateway</a></li>
                                <li class="menu-item"><a href="services.html#payment-orchestration">Payment Orchestration</a></li>
                            </ul>
                        </div>

                        <div class="col col-2">
                            <span class="widget-title">Payment Methods</span>
                            <ul class="menu">
                                <li class="menu-item"><a href="services.html#payment-methods">Credit Cards</a></li>
                                <li class="menu-item"><a href="services.html#payment-methods">Debit Cards</a></li>
                                <li class="menu-item"><a href="services.html#payment-methods">Alternative Payments</a></li>
                            </ul>
                        </div>

                        <div class="col col-3">
                            <span class="widget-title">Industries</span>
                            <ul class="menu">
                                <li class="menu-item"><a href="services.html">E-commerce</a></li>
                                <li class="menu-item"><a href="services.html">Retail</a></li>
                                <li class="menu-item"><a href="services.html">Travel</a></li>
                                <li class="menu-item"><a href="services.html">Digital Services</a></li>
                                <li class="menu-item"><a href="services.html">Other Industries</a></li>
                            </ul>
                        </div>

                        <div class="col col-2">
                            <span class="widget-title">Resources</span>
                            <ul class="menu">
                                <li class="menu-item"><a href="#">API Documentation</a></li>
                                <li class="menu-item"><a href="#">Integration Guides</a></li>
                                <li class="menu-item"><a href="services.html#secure-transactions">PCI Compliance</a></li>
                            </ul>

                            <span class="widget-title">Legal</span>
                            <ul class="menu">
                                <li class="menu-item"><a href="privacy-policy.html">Privacy Policy</a></li>
                                <li class="menu-item"><a href="https://policies.google.com/terms" target="_blank">Terms of Service</a></li>
                                <li class="menu-item"><a href="imprint.html">Imprint / Contact</a></li>
                                <li class="menu-item"><a href="#">AML Policy</a></li>
                            </ul>
                        </div>

                        <div class="col col-1"></div>
                    </div>
                </div>
            </section>

            <section id="footer-bottom">
                <div class="wrapper">
                    <div class="footer-main">
                        <div class="footer-left">
                            <a class="footer-logo" href="index.html">
                                <img src="images/logo-footer.png" alt="SailorPay" width="150">
                            </a>
                        </div>
                        <div class="footer-center">
                            <div class="payment-logos">
                                <img src="images/visa-logo.png" alt="Visa" class="payment-logo-img">
                                <img src="images/mastercard-logo.png" alt="Mastercard" class="payment-logo-img">
                                <img src="images/pci-logo.png" alt="PCI DSS Compliant" class="payment-logo-img">
                                <img src="images/gdbr-logo.png" alt="GDPR" class="payment-logo-img">
                            </div>
                        </div>
                        <div class="footer-right">
                            <div class="footer-contact-info">
                                <a href="mailto:<EMAIL>" class="email-link"><i class="fas fa-envelope"></i></a>
                                <a href="https://www.linkedin.com/company/sailorpay" target="_blank" class="linkedin-link"><i class="fab fa-linkedin-in"></i></a>
                                <a href="https://wa.me/************" target="_blank" class="whatsapp-link"><i class="fab fa-whatsapp"></i></a>
                            </div>
                        </div>
                    </div>
                    <div class="footer-bottom">
                        <p class="disclaimer">&copy; 2023 SailorPay. All Rights Reserved. SailorPay is a registered payment service provider.</p>
                    </div>
                </div>
            </section>
        </footer><!-- #colophon -->
    </div><!-- #page -->

    <!-- Cookie Consent Banner -->
    <div id="cookie-consent" class="cookie-consent">
        <p>This website uses cookies.</p>
        <p>We use cookies to analyze website traffic and optimize your website experience. By accepting our use of cookies, your data will be aggregated with all other user data.</p>
        <div class="cookie-buttons">
            <button id="cookie-decline" class="cookie-button decline">Decline</button>
            <button id="cookie-accept" class="cookie-button accept">Accept</button>
        </div>
    </div>

    <!-- JavaScript for menu functionality -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Hamburger menu toggle
            const menuToggle = document.querySelector('.hamburger');
            const mobileMenu = document.getElementById('mobile-menu');

            if (menuToggle && mobileMenu) {
                menuToggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    menuToggle.classList.toggle('is-active');

                    // Directly manipulate the style for better compatibility
                    if (mobileMenu.style.left === '-210px' || mobileMenu.style.left === '') {
                        mobileMenu.style.left = '0';
                        mobileMenu.style.opacity = '1';
                        mobileMenu.style.visibility = 'visible';
                        mobileMenu.classList.add('active');
                        document.body.classList.add('menu-open'); // Prevent body scrolling
                    } else {
                        mobileMenu.style.left = '-210px';
                        mobileMenu.style.opacity = '0';
                        mobileMenu.style.visibility = 'hidden';
                        mobileMenu.classList.remove('active');
                        document.body.classList.remove('menu-open'); // Re-enable body scrolling
                    }

                    console.log('Hamburger clicked, mobile menu left:', mobileMenu.style.left);
                });
            }

            // Close mobile menu when clicking outside
            document.addEventListener('click', function(event) {
                if (mobileMenu && mobileMenu.classList.contains('active') &&
                    !mobileMenu.contains(event.target) &&
                    !menuToggle.contains(event.target)) {
                    mobileMenu.style.left = '-210px';
                    mobileMenu.style.opacity = '0';
                    mobileMenu.style.visibility = 'hidden';
                    mobileMenu.classList.remove('active');
                    menuToggle.classList.remove('is-active');
                    document.body.classList.remove('menu-open'); // Re-enable body scrolling
                    console.log('Clicked outside, closing menu');
                }
            });

            // Logo animation on scroll - simplified to only affect logo size
            const header = document.querySelector('.site-header');
            let scrollTimer;
            let lastScrollTop = 0;

            function handleScroll() {
                const currentScrollTop = window.scrollY;

                // Only trigger the animation when scrolling down past the threshold
                if (currentScrollTop > 50 && currentScrollTop > lastScrollTop) {
                    if (!header.classList.contains('scrolled')) {
                        header.classList.add('scrolled');
                    }
                } else if (currentScrollTop < 30 || currentScrollTop < lastScrollTop) {
                    if (header.classList.contains('scrolled')) {
                        header.classList.remove('scrolled');
                    }
                }

                lastScrollTop = currentScrollTop;
            }

            // Use passive event listener for better performance
            window.addEventListener('scroll', handleScroll, { passive: true });

            // Mobile dropdown menu
            const mobileMenuItems = document.querySelectorAll('.mobile-menu ul li.has-dropdown');

            mobileMenuItems.forEach(function(item) {
                const link = item.querySelector('a');
                const submenu = item.querySelector('.sub-menu');

                if (link && submenu) {
                    link.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();

                        // Toggle active class
                        item.classList.toggle('active');

                        // Close other open dropdowns
                        mobileMenuItems.forEach(function(otherItem) {
                            if (otherItem !== item) {
                                otherItem.classList.remove('active');
                            }
                        });
                    });
                }
            });

            // Cookie consent
            const cookieConsent = document.getElementById('cookie-consent');
            const acceptButton = document.getElementById('cookie-accept');
            const declineButton = document.getElementById('cookie-decline');

            // Check if user has already made a choice
            if (!localStorage.getItem('cookieConsent')) {
                cookieConsent.classList.add('active');
            }

            acceptButton.addEventListener('click', function() {
                localStorage.setItem('cookieConsent', 'accepted');
                cookieConsent.classList.remove('active');
            });

            declineButton.addEventListener('click', function() {
                localStorage.setItem('cookieConsent', 'declined');
                cookieConsent.classList.remove('active');
            });


        });
    </script>
    <script src="js/modern.js"></script>
</body>
</html>
